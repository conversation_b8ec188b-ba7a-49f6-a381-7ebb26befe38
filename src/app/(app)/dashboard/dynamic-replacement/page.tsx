"use client";

import axios from "axios";
import { useState } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import type { GitHubLinkFormValues } from "@/app/components/github-link-form/create-job-form";
import { CreateJobForm } from "@/app/components/github-link-form/create-job-form";
import { Webhooks } from "@/app/components/Webhooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import { checkFields } from "@/utils/fieldChecker";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import { H1, Body3 } from "@/app/components/app-typography";

export interface PreparedDynamicReplacementContract {
  target: string;
  replacement: string;
  endOfTargetMarker: string;
  targetContract: string;
}
export default function JobsPage() {
  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);
  const [jobId, setJobId] = useState<null | number>(null);

  const { data: allJobs, refetch: refetchJobs } = useGetJobs();
  const { data: organization } = useGetMyOrg();

  const runningJobs = () => {
    return allJobs.filter(
      (job) =>
        job.status === "RUNNING" ||
        job.status === "STARTED" ||
        job.status === "QUEUED"
    );
  };
  const startEchidnaAbiJob = async ({
    pathToTester,
    echidnaConfig,
    contract,
    corpusDir,
    forkBlock,
    forkMode,
    forkReplacement,
    ref,
    repoName,
    rpcUrl,
    testLimit,
    testMode,
    preprocess,
    directory,
    orgName,
    targetCorpus,
    label,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      const runningJobsList = runningJobs()
        .map((job) => `${job.label}-${job.id}`)
        .join(", ");
      alert(
        `You can only run one job at a time, currently running: ${runningJobsList}`
      );
      return;
    }
    const fuzzerArgs = {
      pathToTester,
      config: echidnaConfig,
      contract,
      corpusDir,
      forkBlock,
      forkMode,
      forkReplacement,
      rpcUrl,
      testLimit,
      testMode,
      preprocess,
      targetCorpus,
      label,
      prepareContracts,

      // TODO: Make gov fuzz a default (use prepareContracts.length) and remove the need to explicitly add it as a var
      govFuzz: true,
    };

    const areBasicFieldsOK = checkFields(
      orgName,
      repoName,
      ref,
      prepareContracts
    );
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/echidna`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startFoundryJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    //corpusDir,
    forkBlock,
    forkMode,
    ref,
    repoName,
    //testLimit,
    //testMode,
    preprocess,
    directory,
    orgName,
    rpcUrl,
    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,
    //targetCorpus,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //corpusDir,
      forkBlock,
      forkMode,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      rpcUrl,
      runs,
      seed,
      verbosity,
      testCommand,
      testTarget,
      prepareContracts,

      // TODO: Make gov fuzz a default (use prepareContracts.length) and remove the need to explicitly add it as a var
      govFuzz: true,
    };

    const areBasicFieldsOK = checkFields(
      orgName,
      repoName,
      ref,
      prepareContracts
    );
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/foundry`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startHalmosJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    halmosPrefix,
    halmosArray,
    halmosLoops,
    verbosity,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    const fuzzerArgs = {
      contract,
      preprocess,
      halmosArray,
      halmosLoops,
      halmosPrefix,
      verbosity,
      prepareContracts,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/halmos`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startKontrolJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    kontrolTest,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      kontrolTest,
      prepareContracts,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/kontrol`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startMedusaAbiJob = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    targetCorpus,
    label,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    const fuzzerArgs = {
      timeout,
      config: medusaConfig,
      targetCorpus,
      prepareContracts,

      // TODO: Make gov fuzz a default (use prepareContracts.length) and remove the need to explicitly add it as a var
      govFuzz: true,
    };

    const areBasicFieldsOK = checkFields(
      orgName,
      repoName,
      ref,
      prepareContracts
    );
    if (!areBasicFieldsOK) return;
    console.log("prepareContracts", prepareContracts);
    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/medusa`,
        data: {
          fuzzerArgs,
          preprocess,
          orgName,
          repoName,
          ref,
          directory,
          label,
        },
      });

      // setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const onSubmit =
    env === ENV_TYPE.MEDUSA
      ? startMedusaAbiJob
      : env === ENV_TYPE.ECHIDNA
        ? startEchidnaAbiJob
        : env === ENV_TYPE.FOUNDRY
          ? startFoundryJob
          : env === ENV_TYPE.HALMOS
            ? startHalmosJob
            : startKontrolJob;

  return (
    <div className="flex flex-col gap-6 px-10 py-6">
      <Webhooks />

      <div className="flex flex-col gap-1 self-stretch">
        <H1 color="accent">Dynamic Replacement</H1>
        <div className="flex flex-col gap-2">
          <Body3 color="secondary">
            Dynamic Replacement is in EXPERIMENTAL mode
          </Body3>
          <Body3 color="secondary">
            All variables Dynamically Replaced MUST be in the `Setup.sol` file
          </Body3>
          <Body3 color="secondary">Make sure you have no clashing file!</Body3>
        </div>
      </div>

      <CreateJobForm
        title=""
        submitLabel="Run Job"
        {...{
          env,
          jobId,
          setEnv,
        }}
        onSubmit={onSubmit}
        dynamicReplacement={true}
      />
    </div>
  );
}
