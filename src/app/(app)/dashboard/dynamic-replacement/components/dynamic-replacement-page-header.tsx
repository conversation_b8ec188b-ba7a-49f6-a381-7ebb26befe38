import { H1, Body3 } from "@/app/components/app-typography";
import { DYNAMIC_REPLACEMENT_CONSTANTS } from "../constants";

export function DynamicReplacementPageHeader() {
  const { CONTENT, LAYOUT, COLORS, T<PERSON><PERSON>GRAPHY } = DYNAMIC_REPLACEMENT_CONSTANTS;
  
  return (
    <div className={`flex flex-col self-stretch ${LAYOUT.HEADER_GAP}`}>
      <H1 color={COLORS.TITLE}>
        {CONTENT.TITLE}
      </H1>
      <div className={`flex flex-col ${LAYOUT.DESCRIPTION_GAP}`}>
        {CONTENT.DESCRIPTIONS.map((description, index) => (
          <Body3 
            key={index} 
            color={COLORS.DESCRIPTION}
          >
            {description}
          </Body3>
        ))}
      </div>
    </div>
  );
}
