import type { ReactNode } from "react";
import { DYNAMIC_REPLACEMENT_CONSTANTS } from "../constants";

interface DynamicReplacementPageLayoutProps {
  children: ReactNode;
}

export function DynamicReplacementPageLayout({ children }: DynamicReplacementPageLayoutProps) {
  const { LAYOUT } = DYNAMIC_REPLACEMENT_CONSTANTS;
  
  return (
    <div className={`flex flex-col ${LAYOUT.CONTAINER_GAP} ${LAYOUT.CONTAINER_PADDING_X} ${LAYOUT.CONTAINER_PADDING_Y}`}>
      {children}
    </div>
  );
}
