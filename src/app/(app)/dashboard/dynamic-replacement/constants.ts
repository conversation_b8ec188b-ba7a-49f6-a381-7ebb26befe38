// Dynamic Replacement Page Constants
export const DYNAMIC_REPLACEMENT_CONSTANTS = {
  // Page Layout
  LAYOUT: {
    CONTAINER_PADDING_X: "px-10", // Using design token instead of px-[200px]
    CONTAINER_PADDING_Y: "py-6",
    CONTAINER_GAP: "gap-6",
    HEADER_GAP: "gap-1",
    DESCRIPTION_GAP: "gap-2",
  },
  
  // Typography
  TYPOGRAPHY: {
    TITLE_VARIANT: "highlight-1" as const,
    DESCRIPTION_VARIANT: "body-3" as const,
  },
  
  // Colors
  COLORS: {
    TITLE: "accent-alt-primary", // Using design token instead of #DFDBFA
    DESCRIPTION: "textSecondary",
  },
  
  // Content
  CONTENT: {
    TITLE: "Dynamic Replacement",
    DESCRIPTIONS: [
      "Dynamic Replacement is in EXPERIMENTAL mode",
      "All variables Dynamically Replaced MUST be in the `Setup.sol` file",
      "Make sure you have no clashing file!",
    ],
  },
  
  // Form
  FORM: {
    TITLE: "",
    SUBMIT_LABEL: "Run Job",
  },
  
  // Alert Messages
  ALERTS: {
    TRIAL_LIMIT: (runningJobs: string) => 
      `You can only run one job at a time, currently running: ${runningJobs}`,
    GENERIC_ERROR: (message: string) => `Something went wrong: ${message}`,
    SIMPLE_ERROR: "Something went wrong",
  },
  
  // API Endpoints
  API: {
    ECHIDNA: "/api/jobs/echidna",
    FOUNDRY: "/api/jobs/foundry", 
    HALMOS: "/api/jobs/halmos",
    KONTROL: "/api/jobs/kontrol",
    MEDUSA: "/api/jobs/medusa",
  },
  
  // Job Configuration
  JOB_CONFIG: {
    GOV_FUZZ_DEFAULT: true,
  },
} as const;

// Type exports for better type safety
export type DynamicReplacementConstants = typeof DYNAMIC_REPLACEMENT_CONSTANTS;
